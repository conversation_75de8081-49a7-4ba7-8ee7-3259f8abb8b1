import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { artifactService } from '@/lib/artifacts'

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id, title, content, language } = await req.json()

    if (!id) {
      return NextResponse.json(
        { error: 'Artifact ID required' }, 
        { status: 400 }
      )
    }

    const artifact = await artifactService.update(id, {
      title,
      content,
      language,
    })

    return NextResponse.json({ artifact })
  } catch (error) {
    console.error('Artifact update error:', error)
    return NextResponse.json(
      { error: 'Failed to update artifact' }, 
      { status: 500 }
    )
  }
}
