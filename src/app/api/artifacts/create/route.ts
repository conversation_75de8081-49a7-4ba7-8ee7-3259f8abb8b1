import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { artifactService } from '@/lib/artifacts'

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { title, type, content, language, messageId } = await req.json()

    if (!title || !type || !content) {
      return NextResponse.json(
        { error: 'Missing required fields' }, 
        { status: 400 }
      )
    }

    if (!artifactService.validateContent(type, content)) {
      return NextResponse.json(
        { error: 'Invalid content for artifact type' }, 
        { status: 400 }
      )
    }

    const artifact = await artifactService.create({
      title,
      type,
      content,
      language,
      userId: session.user.id,
      messageId,
    })

    return NextResponse.json({ artifact })
  } catch (error) {
    console.error('Artifact creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create artifact' }, 
      { status: 500 }
    )
  }
}
