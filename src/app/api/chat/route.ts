import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { llmService } from '@/lib/llm'
import { artifactService } from '@/lib/artifacts'

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { messages, files } = await req.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: 'Messages required' }, { status: 400 })
    }

    // Get AI response
    const response = await llmService.chat(messages)
    
    // Check if response contains artifact creation request
    const artifactMatch = response.match(/CREATE_ARTIFACT:\s*({.*?})/s)
    let artifacts = []
    
    if (artifactMatch) {
      try {
        const artifactData = JSON.parse(artifactMatch[1])
        const artifact = await artifactService.create({
          ...artifactData,
          userId: session.user.id,
        })
        artifacts.push(artifact)
      } catch (error) {
        console.error('Artifact creation failed:', error)
      }
    }

    return NextResponse.json({
      message: response.replace(/CREATE_ARTIFACT:\s*{.*?}/s, '').trim(),
      artifacts
    })
  } catch (error) {
    console.error('Chat API error:', error)
    return NextResponse.json(
      { error: 'Failed to process chat request' }, 
      { status: 500 }
    )
  }
}
