import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

export interface ArtifactCreationParams {
  type: 'html' | 'react' | 'svg' | 'code' | 'markdown'
  title: string
  content: string
  language?: string
}

const SYSTEM_PROMPT = `You are an advanced AI assistant capable of creating interactive artifacts. You can:

1. Create HTML, React components, SVG graphics, and code snippets
2. Use web search and file processing tools
3. Execute code in a sandboxed environment
4. Generate data visualizations and interactive content

When creating artifacts:
- Use modern, responsive designs
- Include proper error handling
- Make content interactive when possible
- Follow accessibility best practices
- Use Tailwind CSS for styling

Available tools:
- web_search: Search the internet for current information
- file_upload: Process uploaded files (CSV, JSON, text)
- code_execute: Run JavaScript code safely
- create_artifact: Generate interactive content

Always prioritize user experience and provide complete, functional solutions.`

export class LLMService {
  async chat(messages: ChatMessage[]): Promise<string> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4-turbo-preview",
        messages: [
          { role: "system", content: SYSTEM_PROMPT },
          ...messages
        ],
        max_tokens: 4000,
        temperature: 0.7,
      })

      return response.choices[0]?.message?.content || "I apologize, but I couldn't generate a response."
    } catch (error) {
      console.error('LLM API Error:', error)
      throw new Error('Failed to get AI response')
    }
  }

  async createArtifact(prompt: string, type: string): Promise<ArtifactCreationParams> {
    const messages: ChatMessage[] = [
      {
        role: "user",
        content: `Create a ${type} artifact for: ${prompt}. 
        
        Respond with a JSON object containing:
        - type: "${type}"
        - title: "descriptive title"
        - content: "the actual code/content"
        - language: "programming language if applicable"
        
        Make it production-ready with modern styling and full functionality.`
      }
    ]

    try {
      const response = await this.chat(messages)
      
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0])
      }
      
      // Fallback if JSON parsing fails
      return {
        type: type as any,
        title: "Generated Artifact",
        content: response,
        language: type === 'code' ? 'javascript' : undefined
      }
    } catch (error) {
      console.error('Artifact creation error:', error)
      throw new Error('Failed to create artifact')
    }
  }

  async analyzeQuery(query: string): Promise<{
    needsSearch: boolean
    needsFileProcessing: boolean
    needsCodeExecution: boolean
    suggestedTools: string[]
  }> {
    const keywords = {
      search: ['search', 'find', 'latest', 'current', 'news', 'recent'],
      file: ['file', 'csv', 'json', 'data', 'upload', 'analyze'],
      code: ['run', 'execute', 'calculate', 'compute', 'code']
    }

    const lowerQuery = query.toLowerCase()
    
    return {
      needsSearch: keywords.search.some(k => lowerQuery.includes(k)),
      needsFileProcessing: keywords.file.some(k => lowerQuery.includes(k)),
      needsCodeExecution: keywords.code.some(k => lowerQuery.includes(k)),
      suggestedTools: [
        ...(keywords.search.some(k => lowerQuery.includes(k)) ? ['web_search'] : []),
        ...(keywords.file.some(k => lowerQuery.includes(k)) ? ['file_upload'] : []),
        ...(keywords.code.some(k => lowerQuery.includes(k)) ? ['code_execute'] : [])
      ]
    }
  }
}

export const llmService = new LLMService()
