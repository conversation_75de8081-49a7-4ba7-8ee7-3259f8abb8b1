import { db } from './db'

export type ArtifactType = 'html' | 'react' | 'svg' | 'code' | 'markdown'

export interface Artifact {
  id: string
  title: string
  type: ArtifactType
  content: string
  language?: string
  version: number
  createdAt: Date
  updatedAt: Date
}

export class ArtifactService {
  async create(params: {
    title: string
    type: ArtifactType
    content: string
    language?: string
    userId: string
    messageId?: string
  }): Promise<Artifact> {
    try {
      const artifact = await db.artifact.create({
        data: {
          title: params.title,
          type: params.type,
          content: params.content,
          language: params.language,
          userId: params.userId,
          messageId: params.messageId,
        }
      })

      return artifact
    } catch (error) {
      console.error('Artifact creation error:', error)
      throw new Error('Failed to create artifact')
    }
  }

  async update(id: string, updates: {
    title?: string
    content?: string
    language?: string
  }): Promise<Artifact> {
    try {
      const artifact = await db.artifact.update({
        where: { id },
        data: {
          ...updates,
          version: { increment: 1 },
          updatedAt: new Date(),
        }
      })

      return artifact
    } catch (error) {
      console.error('Artifact update error:', error)
      throw new Error('Failed to update artifact')
    }
  }

  async findByUser(userId: string): Promise<Artifact[]> {
    return db.artifact.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })
  }

  async findById(id: string): Promise<Artifact | null> {
    return db.artifact.findUnique({
      where: { id }
    })
  }

  validateContent(type: ArtifactType, content: string): boolean {
    switch (type) {
      case 'html':
        return content.includes('<') && content.includes('>')
      case 'react':
        return content.includes('export') || content.includes('function') || content.includes('=>')
      case 'svg':
        return content.includes('<svg') && content.includes('</svg>')
      case 'code':
        return content.length > 0
      case 'markdown':
        return content.length > 0
      default:
        return false
    }
  }
}

export const artifactService = new ArtifactService()
