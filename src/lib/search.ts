interface SearchResult {
  title: string
  url: string
  snippet: string
  publishedAt?: string
}

export class SearchService {
  private apiKey = process.env.SEARCH_API_KEY
  private searchEngineId = process.env.SEARCH_ENGINE_ID

  async search(query: string, limit = 10): Promise<SearchResult[]> {
    if (!this.apiKey || !this.searchEngineId) {
      throw new Error('Search API not configured')
    }

    try {
      const url = `https://www.googleapis.com/customsearch/v1?key=${this.apiKey}&cx=${this.searchEngineId}&q=${encodeURIComponent(query)}&num=${limit}`
      
      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error?.message || 'Search API error')
      }

      return data.items?.map((item: any) => ({
        title: item.title,
        url: item.link,
        snippet: item.snippet,
        publishedAt: item.pagemap?.metatags?.[0]?.['article:published_time']
      })) || []
    } catch (error) {
      console.error('Search error:', error)
      return []
    }
  }

  async fetchPage(url: string): Promise<string> {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; AIBot/1.0)',
        },
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      return await response.text()
    } catch (error) {
      console.error('Page fetch error:', error)
      throw new Error('Failed to fetch page content')
    }
  }
}

export const searchService = new SearchService()
