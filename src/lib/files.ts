import { writeFile, readFile } from 'fs/promises'
import { join } from 'path'
import { v4 as uuidv4 } from 'uuid'

export interface ProcessedFile {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  content?: any
  preview?: string
}

export class FileService {
  private uploadDir = join(process.cwd(), 'uploads')

  async saveFile(file: File): Promise<ProcessedFile> {
    const id = uuidv4()
    const filename = `${id}-${file.name}`
    const filepath = join(this.uploadDir, filename)

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    await writeFile(filepath, buffer)

    return {
      id,
      filename,
      originalName: file.name,
      mimeType: file.type,
      size: file.size,
    }
  }

  async processFile(fileId: string): Promise<any> {
    const filepath = join(this.uploadDir, fileId)
    
    try {
      const content = await readFile(filepath, 'utf-8')
      
      // Process based on file type
      if (fileId.endsWith('.json')) {
        return JSON.parse(content)
      }
      
      if (fileId.endsWith('.csv')) {
        return this.parseCSV(content)
      }
      
      return content
    } catch (error) {
      throw new Error('Failed to process file')
    }
  }

  private parseCSV(content: string): any[] {
    const lines = content.trim().split('\n')
    const headers = lines[0].split(',').map(h => h.trim())
    
    return lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.trim())
      const row: any = {}
      
      headers.forEach((header, index) => {
        const value = values[index] || ''
        row[header] = isNaN(Number(value)) ? value : Number(value)
      })
      
      return row
    })
  }

  async analyzeFile(fileId: string): Promise<{
    type: string
    rows?: number
    columns?: string[]
    sample?: any
  }> {
    const data = await this.processFile(fileId)
    
    if (Array.isArray(data)) {
      return {
        type: 'tabular',
        rows: data.length,
        columns: data.length > 0 ? Object.keys(data[0]) : [],
        sample: data.slice(0, 3)
      }
    }
    
    if (typeof data === 'object') {
      return {
        type: 'json',
        sample: data
      }
    }
    
    return {
      type: 'text',
      sample: data.slice(0, 500)
    }
  }
}

export const fileService = new FileService()
